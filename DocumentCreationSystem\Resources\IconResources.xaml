<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <!-- 应用程序图标资源 -->
    
    <!-- 主应用程序图标 (32x32) -->
    <BitmapImage x:Key="AppIcon32" UriSource="pack://application:,,,/DocumentCreationSystem;component/Resources/AppIcon_32.png"/>
    
    <!-- 小图标 (16x16) -->
    <BitmapImage x:Key="AppIcon16" UriSource="pack://application:,,,/DocumentCreationSystem;component/Resources/AppIcon_16.png"/>
    
    <!-- 中等图标 (48x48) -->
    <BitmapImage x:Key="AppIcon48" UriSource="pack://application:,,,/DocumentCreationSystem;component/Resources/AppIcon_48.png"/>
    
    <!-- 大图标 (64x64) -->
    <BitmapImage x:Key="AppIcon64" UriSource="pack://application:,,,/DocumentCreationSystem;component/Resources/AppIcon_64.png"/>
    
    <!-- 超大图标 (128x128) -->
    <BitmapImage x:Key="AppIcon128" UriSource="pack://application:,,,/DocumentCreationSystem;component/Resources/AppIcon_128.png"/>
    
    <!-- 简化版小图标 -->
    <BitmapImage x:Key="AppIconSimple16" UriSource="pack://application:,,,/DocumentCreationSystem;component/Resources/AppIcon_Simple_16.png"/>
    <BitmapImage x:Key="AppIconSimple24" UriSource="pack://application:,,,/DocumentCreationSystem;component/Resources/AppIcon_Simple_24.png"/>
    <BitmapImage x:Key="AppIconSimple32" UriSource="pack://application:,,,/DocumentCreationSystem;component/Resources/AppIcon_Simple_32.png"/>
    
    <!-- SVG图标作为DrawingImage资源 -->
    <DrawingImage x:Key="AppIconVector">
        <DrawingImage.Drawing>
            <DrawingGroup>
                <!-- 背景圆形 -->
                <GeometryDrawing>
                    <GeometryDrawing.Geometry>
                        <EllipseGeometry Center="32,32" RadiusX="30" RadiusY="30"/>
                    </GeometryDrawing.Geometry>
                    <GeometryDrawing.Brush>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                            <GradientStop Color="#4A90E2" Offset="0"/>
                            <GradientStop Color="#357ABD" Offset="1"/>
                        </LinearGradientBrush>
                    </GeometryDrawing.Brush>
                </GeometryDrawing>
                
                <!-- 文档图标 -->
                <GeometryDrawing>
                    <GeometryDrawing.Geometry>
                        <RectangleGeometry Rect="12,8,24,32" RadiusX="2" RadiusY="2"/>
                    </GeometryDrawing.Geometry>
                    <GeometryDrawing.Brush>
                        <SolidColorBrush Color="White"/>
                    </GeometryDrawing.Brush>
                </GeometryDrawing>
                
                <!-- 文档折角 -->
                <GeometryDrawing>
                    <GeometryDrawing.Geometry>
                        <PathGeometry>
                            <PathFigure StartPoint="32,8">
                                <LineSegment Point="36,12"/>
                                <LineSegment Point="32,12"/>
                                <LineSegment Point="32,8"/>
                            </PathFigure>
                        </PathGeometry>
                    </GeometryDrawing.Geometry>
                    <GeometryDrawing.Brush>
                        <SolidColorBrush Color="#E8E8E8"/>
                    </GeometryDrawing.Brush>
                </GeometryDrawing>
                
                <!-- AI标识 -->
                <GeometryDrawing>
                    <GeometryDrawing.Geometry>
                        <EllipseGeometry Center="48,48" RadiusX="8" RadiusY="8"/>
                    </GeometryDrawing.Geometry>
                    <GeometryDrawing.Brush>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                            <GradientStop Color="#7B68EE" Offset="0"/>
                            <GradientStop Color="#6A5ACD" Offset="1"/>
                        </LinearGradientBrush>
                    </GeometryDrawing.Brush>
                </GeometryDrawing>
            </DrawingGroup>
        </DrawingImage.Drawing>
    </DrawingImage>
    
    <!-- 功能图标样式 -->
    <Style x:Key="AppIconStyle" TargetType="Image">
        <Setter Property="Width" Value="32"/>
        <Setter Property="Height" Value="32"/>
        <Setter Property="Source" Value="{StaticResource AppIcon32}"/>
        <Style.Triggers>
            <Trigger Property="Width" Value="16">
                <Setter Property="Source" Value="{StaticResource AppIconSimple16}"/>
            </Trigger>
            <Trigger Property="Width" Value="24">
                <Setter Property="Source" Value="{StaticResource AppIconSimple24}"/>
            </Trigger>
            <Trigger Property="Width" Value="48">
                <Setter Property="Source" Value="{StaticResource AppIcon48}"/>
            </Trigger>
            <Trigger Property="Width" Value="64">
                <Setter Property="Source" Value="{StaticResource AppIcon64}"/>
            </Trigger>
        </Style.Triggers>
    </Style>
    
</ResourceDictionary>
